#include <metal_stdlib>
#include <simd/simd.h>

using namespace metal;

constant float2 _25 = {};
constant float4 _26 = {};

struct main0_out
{
    float4 out_var_TEXCOORD0 [[user(locn0)]];
    float4 gl_Position [[position]];
};

vertex main0_out main0(uint gl_VertexIndex [[vertex_id]])
{
    main0_out out = {};
    float4 _47;
    float2 _48;
    if (gl_VertexIndex == 0u)
    {
        _47 = float4(1.0, 0.0, 0.0, 1.0);
        _48 = float2(-1.0);
    }
    else
    {
        float4 _45;
        float2 _46;
        if (gl_VertexIndex == 1u)
        {
            _45 = float4(0.0, 1.0, 0.0, 1.0);
            _46 = float2(1.0, -1.0);
        }
        else
        {
            bool _40 = gl_VertexIndex == 2u;
            _45 = select(_26, float4(0.0, 0.0, 1.0, 1.0), bool4(_40));
            _46 = select(_25, float2(0.0, 1.0), bool2(_40));
        }
        _47 = _45;
        _48 = _46;
    }
    out.out_var_TEXCOORD0 = _47;
    out.gl_Position = float4(_48, 0.0, 1.0);
    return out;
}

