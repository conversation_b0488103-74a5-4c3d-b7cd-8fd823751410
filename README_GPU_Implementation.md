# SDL3 Pong: Default Renderer vs GPU Implementation

This project demonstrates the same Pong game implemented using two different SDL3 rendering approaches:

1. **Default Renderer** (`sdl3_pong_default_renderer.c`) - Uses SDL3's traditional 2D renderer
2. **GPU Renderer** (`sdl3_pong_GPU.c`) - Uses SDL3's new GPU API for hardware-accelerated rendering

## Key Differences

### Default Renderer Approach
- Uses `SDL_CreateWindowAndRenderer()` for simple setup
- Renders using `SDL_RenderFillRect()` and similar 2D functions
- Automatic batching and optimization by SDL
- Simpler code, easier to understand
- Good for 2D games and UI applications

### GPU Renderer Approach  
- Uses `SDL_CreateGPUDevice()` and `SDL_ClaimWindowForGPUDevice()`
- Manual vertex buffer and index buffer management
- Command buffer-based rendering
- More control over GPU resources
- Better for complex 3D graphics and compute shaders
- Requires understanding of modern graphics APIs (Vulkan/D3D12/Metal concepts)

## Architecture Comparison

### Default Renderer Structure
```c
SDL_Window *window;
SDL_Renderer *renderer;

// Simple rendering
SDL_SetRenderDrawColor(renderer, 255, 255, 255, 255);
SDL_RenderFillRect(renderer, &rect);
SDL_RenderPresent(renderer);
```

### GPU Renderer Structure
```c
SDL_GPUDevice *device;
SDL_Window *window;
SDL_GPUBuffer *vertex_buffer;
SDL_GPUBuffer *index_buffer;
SDL_GPUGraphicsPipeline *pipeline;

// Complex rendering pipeline
SDL_GPUCommandBuffer *cmdbuf = SDL_AcquireGPUCommandBuffer(device);
SDL_GPURenderPass *pass = SDL_BeginGPURenderPass(cmdbuf, &color_target, 1, NULL);
// Bind resources and draw
SDL_EndGPURenderPass(pass);
SDL_SubmitGPUCommandBuffer(cmdbuf);
```

## Game Features (Both Versions)

- Single-player Pong with left paddle controlled by W/S keys
- Mouse and touch input support
- Ball physics with collision detection
- Score tracking
- Safe area support for mobile devices
- Background/foreground state handling

## Building

### Build Default Renderer Version
```bash
# Edit CMakeLists.txt to uncomment default renderer line
cmake -B build
cd build
ninja
```

### Build GPU Version
```bash
# Edit CMakeLists.txt to uncomment GPU line (current default)
cmake -B build  
cd build
ninja
```

## GPU Implementation Notes

The GPU version demonstrates:

1. **Device Creation**: Setting up SDL3 GPU device with multiple shader format support
2. **Resource Management**: Creating and managing vertex/index buffers
3. **Command Buffer Pattern**: Modern GPU command recording and submission
4. **Transfer Buffers**: Uploading data from CPU to GPU memory
5. **Render Passes**: Structured rendering with color targets

### Simplified GPU Implementation

Note: This GPU implementation is simplified for educational purposes. A full production GPU renderer would include:

- Custom vertex and fragment shaders (HLSL/GLSL/MSL)
- Proper graphics pipeline creation with shader binding
- Uniform buffers for transformation matrices
- Texture support for sprites
- More efficient batching strategies

## Performance Considerations

### Default Renderer
- ✅ Optimized by SDL for 2D rendering
- ✅ Automatic batching and state management
- ✅ Cross-platform without GPU driver concerns
- ❌ Limited to 2D rendering capabilities

### GPU Renderer  
- ✅ Direct hardware access for maximum performance
- ✅ Supports compute shaders and 3D rendering
- ✅ Fine-grained control over GPU resources
- ❌ More complex code and debugging
- ❌ Requires modern GPU drivers
- ❌ Platform-specific shader compilation needed

## When to Use Each Approach

### Use Default Renderer When:
- Building 2D games or applications
- Rapid prototyping
- Cross-platform compatibility is critical
- Team has limited graphics programming experience

### Use GPU Renderer When:
- Building 3D applications or games
- Need compute shader support
- Require maximum performance
- Have complex rendering requirements
- Team has graphics programming expertise

## Learning Path

1. **Start with Default Renderer** - Learn SDL3 basics and game structure
2. **Study GPU Basics** - Understand modern graphics pipeline concepts
3. **Implement Simple GPU Examples** - Clear screen, draw triangle, textured quad
4. **Build Complex GPU Applications** - Full games with shaders and effects

This project serves as a practical comparison between SDL3's rendering approaches, helping developers choose the right tool for their specific needs.
