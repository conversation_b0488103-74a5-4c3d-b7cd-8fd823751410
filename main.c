/*
 * Simple Pong game using SDL3 callback main style.
 * Controls: W/S for left paddle.
 */

#define SDL_MAIN_USE_CALLBACKS 1
#include <SDL3/SDL.h>
#include <SDL3/SDL_main.h>
#include <stdlib.h>
#include <time.h>
#include <stdbool.h>
#include <math.h>


#define PADDLE_WIDTH 16
#define PADDLE_HEIGHT 96
#define BALL_SIZE 16
#define PADDLE_SPEED 400.0f
#define BALL_SPEED 350.0f

static SDL_Window *window = NULL;
static SDL_Renderer *renderer = NULL;

// Game state
static struct {
    float y;
} paddle1 = {0}; // Only left paddle remains

static struct {
    float x, y;
    float vx, vy;
} ball = {0};

static int score = 0; // Only one score for the player
static int up1 = 0, down1 = 0;

typedef struct {
    bool app_in_background;
    int WINDOW_WIDTH;
    int WINDOW_HEIGHT;
    float y_offset;  // Safe area top offset
} AppContext;

static void set_paddle_y_from_pointer(float pointer_y, int window_height, float y_offset) {
    // Adjust pointer position to account for safe area offset
    float adjusted_y = pointer_y - y_offset;
    paddle1.y = adjusted_y - PADDLE_HEIGHT / 2;
    if (paddle1.y < 0) paddle1.y = 0;
    if (paddle1.y > window_height - PADDLE_HEIGHT) paddle1.y = window_height - PADDLE_HEIGHT;
}

SDL_AppResult SDL_AppInit(void **appstate, int argc, char *argv[])
{
    static AppContext context = {0};
    *appstate = &context;

    // Initialize window dimensions
    context.WINDOW_WIDTH = 800;
    context.WINDOW_HEIGHT = 600;

    SDL_SetAppMetadata("Pong Game", "1.0", "com.example.pong");

    if (!SDL_Init(SDL_INIT_VIDEO)) {
        SDL_Log("Couldn't initialize SDL: %s", SDL_GetError());
        return SDL_APP_FAILURE;
    }

    if (!SDL_CreateWindowAndRenderer("Pong", context.WINDOW_WIDTH, context.WINDOW_HEIGHT, 0, &window, &renderer)) {
        SDL_Log("Couldn't create window/renderer: %s", SDL_GetError());
        return SDL_APP_FAILURE;
    }

    srand((unsigned int)time(NULL));
    paddle1.y = (context.WINDOW_HEIGHT - PADDLE_HEIGHT) / 2;
    ball.x = (context.WINDOW_WIDTH - BALL_SIZE) / 2;
    ball.y = (context.WINDOW_HEIGHT - BALL_SIZE) / 2;
    ball.vx = BALL_SPEED * (rand() % 2 ? 1 : -1);
    ball.vy = BALL_SPEED * ((rand() % 2 ? 1 : -1) * 0.5f);

    return SDL_APP_CONTINUE;
}

SDL_AppResult SDL_AppEvent(void *appstate, SDL_Event *event)
{
    AppContext *context = (AppContext *)appstate;
    if (event->type == SDL_EVENT_QUIT) {
        return SDL_APP_SUCCESS;
    }
    // Handle app going to background/foreground
    if (event->type == SDL_EVENT_WILL_ENTER_BACKGROUND) {
        SDL_Log("App entering background");
        context->app_in_background = true;
        return SDL_APP_CONTINUE;
    }
    if (event->type == SDL_EVENT_DID_ENTER_FOREGROUND) {
        SDL_Log("App entering foreground");
        context->app_in_background = false;
        return SDL_APP_CONTINUE;
    }
    if (event->type == SDL_EVENT_KEY_DOWN || event->type == SDL_EVENT_KEY_UP) {
        int down = (event->type == SDL_EVENT_KEY_DOWN);
        SDL_Scancode sc = event->key.scancode;
        if (sc == SDL_SCANCODE_W) up1 = down;
        if (sc == SDL_SCANCODE_S) down1 = down;
    }
    // Mouse motion or button down
    if (event->type == SDL_EVENT_MOUSE_MOTION || event->type == SDL_EVENT_MOUSE_BUTTON_DOWN) {
        set_paddle_y_from_pointer((float)event->motion.y, context->WINDOW_HEIGHT, context->y_offset);
    }
    // Touch motion or finger down
    if (event->type == SDL_EVENT_FINGER_MOTION || event->type == SDL_EVENT_FINGER_DOWN) {
        // For touch, we need to convert from normalized coordinates to screen coordinates first
        SDL_GetWindowSize(window, &context->WINDOW_WIDTH, &context->WINDOW_HEIGHT);
        float screen_y = event->tfinger.y * context->WINDOW_HEIGHT;
        set_paddle_y_from_pointer(screen_y, context->WINDOW_HEIGHT, context->y_offset);
    }
    return SDL_APP_CONTINUE;
}

SDL_AppResult SDL_AppIterate(void *appstate)
{
    AppContext *context = (AppContext *)appstate;
    if (context && context->app_in_background) {
        // Skip game logic and rendering if app is in background
        return SDL_APP_CONTINUE;
    }

    SDL_GetWindowSize(window, &context->WINDOW_WIDTH, &context->WINDOW_HEIGHT);

    // Declare variables for safe area positioning
    float x_offset, y_offset, scale = 1.0f;

    // Manual safe area calculation for iOS - use generous insets to ensure no overlap
    int top_inset = 0, bottom_inset = 0, left_inset = 0, right_inset = 0;

    // Apply safe insets for any mobile-like device (portrait orientation with tall aspect ratio)
    if (context->WINDOW_HEIGHT > context->WINDOW_WIDTH) {
        // Use generous insets to ensure we avoid camera area and rounded corners
        top_inset = (int)(context->WINDOW_HEIGHT * 0.12f);    // 12% from top for notch/dynamic island
        bottom_inset = (int)(context->WINDOW_HEIGHT * 0.08f); // 8% from bottom for home indicator
        left_inset = (int)(context->WINDOW_WIDTH * 0.05f);   // 5% from left for rounded corners
        right_inset = (int)(context->WINDOW_WIDTH * 0.05f);  // 5% from right for rounded corners

        // Debug output
        SDL_Log("Applying safe area insets: top=%d, bottom=%d, left=%d, right=%d (window: %dx%d)",
                top_inset, bottom_inset, left_inset, right_inset, context->WINDOW_WIDTH, context->WINDOW_HEIGHT);
    }

    // Apply safe area insets
    x_offset = (float)left_inset;
    y_offset = (float)top_inset;
    context->WINDOW_WIDTH = context->WINDOW_WIDTH - left_inset - right_inset;
    context->WINDOW_HEIGHT = context->WINDOW_HEIGHT - top_inset - bottom_inset;
    context->y_offset = y_offset;  // Store for use in event handling

    // Debug output for safe area
    static bool logged_once = false;
    if (!logged_once) {
        SDL_Log("Safe area: offset=(%.0f,%.0f), size=(%dx%d)", x_offset, y_offset, context->WINDOW_WIDTH, context->WINDOW_HEIGHT);
        logged_once = true;
    }

    static Uint64 last_ticks = 0;
    Uint64 now_ticks = SDL_GetTicks();
    float dt = (now_ticks - last_ticks) / 1000.0f;
    if (dt > 0.05f) dt = 0.05f;
    last_ticks = now_ticks;

    // Move paddle
    if (up1) paddle1.y -= PADDLE_SPEED * dt;
    if (down1) paddle1.y += PADDLE_SPEED * dt;
    if (paddle1.y < 0) paddle1.y = 0;
    if (paddle1.y > context->WINDOW_HEIGHT - PADDLE_HEIGHT) paddle1.y = context->WINDOW_HEIGHT - PADDLE_HEIGHT;

    // Move ball
    ball.x += ball.vx * dt;
    ball.y += ball.vy * dt;

    // Ball collision with top/bottom
    if (ball.y < 0) { ball.y = 0; ball.vy = -ball.vy; }
    if (ball.y > context->WINDOW_HEIGHT - BALL_SIZE) { ball.y = context->WINDOW_HEIGHT - BALL_SIZE; ball.vy = -ball.vy; }

    // Ball collision with left paddle
    if (ball.x < PADDLE_WIDTH && ball.y + BALL_SIZE > paddle1.y && ball.y < paddle1.y + PADDLE_HEIGHT) {
        ball.x = PADDLE_WIDTH;
        ball.vx = -ball.vx;
        score++; // Increase score for successful hit
    }

    // Ball collision with left wall (missed paddle)
    if (ball.x < 0) {
        // Reset ball to center
        ball.x = (context->WINDOW_WIDTH - BALL_SIZE) / 2;
        ball.y = (context->WINDOW_HEIGHT - BALL_SIZE) / 2;
        ball.vx = BALL_SPEED * (rand() % 2 ? 1 : -1);
        ball.vy = BALL_SPEED * ((rand() % 2 ? 1 : -1) * 0.5f);
        score = 0; // Reset score on miss
    }

    // Ball collision with right wall (now a solid wall)
    if (ball.x > context->WINDOW_WIDTH - BALL_SIZE) {
        ball.x = context->WINDOW_WIDTH - BALL_SIZE;
        ball.vx = -ball.vx;
    }

    // Render
    SDL_SetRenderDrawColor(renderer, 0, 0, 0, 255);
    SDL_RenderClear(renderer);
    SDL_SetRenderScale(renderer, scale, scale);
    SDL_SetRenderViewport(renderer, &(SDL_Rect){(int)x_offset, (int)y_offset, context->WINDOW_WIDTH, context->WINDOW_HEIGHT});
    // Draw paddle
    SDL_FRect p1 = {0, paddle1.y, PADDLE_WIDTH, PADDLE_HEIGHT};
    SDL_SetRenderDrawColor(renderer, 255, 255, 255, 255);
    SDL_RenderFillRect(renderer, &p1);
    // Draw ball
    SDL_FRect b = {ball.x, ball.y, BALL_SIZE, BALL_SIZE};
    SDL_RenderFillRect(renderer, &b);
    // Draw center line
    for (int y = 0; y < context->WINDOW_HEIGHT; y += 24) {
        SDL_FRect line = {context->WINDOW_WIDTH/2 - 2, y, 4, 12};
        SDL_RenderFillRect(renderer, &line);
    }
    SDL_RenderPresent(renderer);
    // Reset scale and viewport for next frame
    SDL_SetRenderScale(renderer, 1.0f, 1.0f);
    SDL_SetRenderViewport(renderer, NULL);
    return SDL_APP_CONTINUE;
}

void SDL_AppQuit(void *appstate, SDL_AppResult result)
{
    // SDL will clean up the window/renderer for us.
}
