{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-f5ebdc15457944623624.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "sdl3_pong", "targetIndexes": [0]}], "targets": [{"directoryIndex": 0, "id": "sdl3_pong::@6890427a1f51a3e7e1df", "jsonFile": "target-sdl3_pong-Debug-c83ae4aa18313667081d.json", "name": "sdl3_pong", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/Users/<USER>/Documents/try/sdlProject/SDL32D/sdl3Copilot/build", "source": "/Users/<USER>/Documents/try/sdlProject/SDL32D/sdl3Copilot"}, "version": {"major": 2, "minor": 8}}